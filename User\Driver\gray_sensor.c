#include "gray_sensor.h"


volatile int8_t error; // 灰度传感器传回来的error
volatile uint8_t a;




//PIN_CLK_GPIO_Port  PIN_CLK_Pin

//PIN_FOWARD_DAT_GPIO_Port  PIN_FOWARD_DAT_Pin



// 添加DWT计数器头文件
#include "core_cm4.h" // 根据你的MCU型号可能是core_cm3.h或core_cm7.h

// 添加微秒延时函数
static inline void delay_us(uint32_t us)
{
    uint32_t start = DWT->CYCCNT;
    uint32_t cycles = us * (SystemCoreClock / 1000000);
    
    while (DWT->CYCCNT - start < cycles);
}






/* 基本时序操作 */
static void SW_IIC_Delay(void)
{
    delay_us(10);  // 根据实际I2C速度调整延时
}

/* 软件I2C基础函数 */
void SW_IIC_Start(void)
{
    SW_SDA_HIGH();  // 软件IIC的SDA置高
    SW_SCL_HIGH();  // 软件IIC的SCL置高
    SW_IIC_Delay();
    SW_SDA_LOW();   // 软件IIC的SDA置低
    SW_IIC_Delay();
    SW_SCL_LOW();   // 软件IIC的SCL置低
}

void SW_IIC_Stop(void)
{
    SW_SDA_LOW();   // 软件IIC的SDA置低
    SW_IIC_Delay();
    SW_SCL_HIGH();  // 软件IIC的SCL置高
    SW_IIC_Delay();
    SW_SDA_HIGH();  // 软件IIC的SDA置高
    SW_IIC_Delay();
}

unsigned char SW_IIC_WaitAck(void)
{
    unsigned char ack;
    SW_SDA_HIGH();  // 软件IIC的SDA置高（释放总线）
    SW_SCL_HIGH();  // 软件IIC的SCL置高（等待从机应答）
    SW_IIC_Delay();
    ack = SW_READ_SDA();  // 读取软件IIC的SDA电平
    SW_SCL_LOW();   // 软件IIC的SCL置低
    SW_IIC_Delay();
    return ack;
}

void SW_IIC_SendAck(void)
{
    SW_SDA_LOW();   // 软件IIC的SDA置低（发送应答）
    SW_SCL_HIGH();  // 软件IIC的SCL置高（让从机读取应答）
    SW_IIC_Delay();
    SW_SCL_LOW();   // 软件IIC的SCL置低
    SW_SDA_HIGH();  // 软件IIC的SDA释放（恢复高电平）
}

void SW_IIC_SendNAck(void)
{
    SW_SDA_HIGH();  // 软件IIC的SDA置高（发送非应答）
    SW_SCL_HIGH();  // 软件IIC的SCL置高（让从机读取非应答）
    SW_IIC_Delay();
    SW_SCL_LOW();   // 软件IIC的SCL置低
}

unsigned char SW_IIC_SendByte(unsigned char dat)
{
    for(unsigned char i = 0; i < 8; i++) {
        // 根据数据位控制软件IIC的SDA电平
        (dat & 0x80) ? SW_SDA_HIGH() : SW_SDA_LOW();
        dat <<= 1;
        SW_SCL_HIGH();  // 软件IIC的SCL置高（从机读取数据）
        SW_IIC_Delay();
        SW_SCL_LOW();   // 软件IIC的SCL置低（准备下一位数据）
        SW_IIC_Delay();
    }
    return SW_IIC_WaitAck();
}

unsigned char SW_IIC_RecvByte(void)
{
    unsigned char dat = 0;
    SW_SDA_HIGH();  // 软件IIC的SDA置高（释放总线，准备接收）
    
    /* 接收数据前切换软件IIC的SDA为输入模式 */
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = SW_SDA_PIN;  // 软件IIC的SDA引脚定义
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(SW_SDA_PORT, &GPIO_InitStruct);  // 软件IIC的SDA端口
    
    for(unsigned char i = 0; i < 8; i++) {
        dat <<= 1;
        SW_SCL_HIGH();  // 软件IIC的SCL置高（主机读取数据）
        SW_IIC_Delay();
        if(SW_READ_SDA()) dat |= 0x01;  // 读取软件IIC的SDA电平
        SW_SCL_LOW();   // 软件IIC的SCL置低（准备下一位数据）
        SW_IIC_Delay();
    }
    
    // 接收完成后切换软件IIC的SDA为开漏输出模式
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD;
    HAL_GPIO_Init(SW_SDA_PORT, &GPIO_InitStruct);
    return dat;
}

/* 应用层函数（保持逻辑不变，仅使用带SW_前缀的宏） */
unsigned char SW_IIC_ReadByte(unsigned char Salve_Address)
{
    unsigned char dat;
    
    SW_IIC_Start();
    SW_IIC_SendByte(Salve_Address | 0x01);  // 读模式
    dat = SW_IIC_RecvByte();
    SW_IIC_SendNAck();
    SW_IIC_Stop();
    
    return dat;
}

unsigned char SW_IIC_ReadBytes(unsigned char Salve_Address, unsigned char Reg_Address, 
                           unsigned char *Result, unsigned char len)
{
    SW_IIC_Start();
    if(SW_IIC_SendByte(Salve_Address & 0xFE)) {  // 写模式
        SW_IIC_Stop();
        return 0;
    }
    if(SW_IIC_SendByte(Reg_Address)) {
        SW_IIC_Stop();
        return 0;
    }
    SW_IIC_Start();
    if(SW_IIC_SendByte(Salve_Address | 0x01)) {  // 读模式
        SW_IIC_Stop();
        return 0;
    }
    
    for(unsigned char i = 0; i < len; i++) {
        Result[i] = SW_IIC_RecvByte();
        (i == len-1) ? SW_IIC_SendNAck() : SW_IIC_SendAck();
    }
    SW_IIC_Stop();
    return 1;
}

unsigned char SW_IIC_WriteByte(unsigned char Salve_Address, unsigned char Reg_Address, 
                           unsigned char data)
{
    SW_IIC_Start();
    if(SW_IIC_SendByte(Salve_Address & 0xFE)) {  // 写模式
        SW_IIC_Stop();
        return 0;
    }
    if(SW_IIC_SendByte(Reg_Address)) {
        SW_IIC_Stop();
        return 0;
    }
    if(SW_IIC_SendByte(data)) {
        SW_IIC_Stop();
        return 0;
    }
    SW_IIC_Stop();
    return 1;
}

unsigned char SW_IIC_WriteBytes(unsigned char Salve_Address, unsigned char Reg_Address,
                            unsigned char *data, unsigned char len)
{
    SW_IIC_Start();
    if(SW_IIC_SendByte(Salve_Address & 0xFE)) {
        SW_IIC_Stop();
        return 0;
    }
    if(SW_IIC_SendByte(Reg_Address)) {
        SW_IIC_Stop();
        return 0;
    }
    
    for(unsigned char i = 0; i < len; i++) {
        if(SW_IIC_SendByte(data[i])) {
            SW_IIC_Stop();
            return 0;
        }
    }
    SW_IIC_Stop();
    return 1;
}

unsigned char SW_Ping(void)
{
    unsigned char dat;
    SW_IIC_ReadBytes(GW_GRAY_ADDR_DEF<<1, GW_GRAY_PING, &dat, 1);
    if(dat == GW_GRAY_PING_OK) {
        return 0;
    } else {
        return 1;
    }
}

unsigned char SW_IIC_Get_Digtal(void)
{
    unsigned char dat;
    SW_IIC_ReadBytes(GW_GRAY_ADDR_DEF<<1, GW_GRAY_DIGITAL_MODE, &dat, 1);
    return dat;
}

unsigned char SW_IIC_Get_Anolog(unsigned char *Result, unsigned char len)
{
    if(SW_IIC_ReadBytes(GW_GRAY_ADDR_DEF<<1, GW_GRAY_ANALOG_BASE_, Result, len)) {
        return 1;
    } else {
        return 0;
    }
}

unsigned char SW_IIC_Get_Single_Anolog(unsigned char Channel)
{
    unsigned char dat;
    SW_IIC_ReadBytes(GW_GRAY_ADDR_DEF<<1, GW_GRAY_ANALOG(Channel), &dat, 1);
    return dat;
}

unsigned char SW_IIC_Anolog_Normalize(uint8_t Normalize_channel)
{
    return SW_IIC_WriteBytes(GW_GRAY_ADDR_DEF<<1, 0xCF, &Normalize_channel, 1);
}

unsigned short SW_IIC_Get_Offset(void)
{
    unsigned char dat;
    SW_IIC_ReadBytes(GW_GRAY_ADDR_DEF<<1, Offset, &dat, 1);
    return dat;
}







/**** 读取灰度传感器数据 ****/

uint8_t gray_serial_forward_read(void)
{
    uint16_t ret = SW_IIC_Get_Digtal();
//    uint8_t ret_8 = 0;
//    
//    // 确保CLK初始状态为高
//    HAL_GPIO_WritePin(PIN_CLK_GPIO_Port, PIN_CLK_Pin, GPIO_PIN_SET);
//    
//    for(int i = 0; i < 8; i++)
//    {
//        // 时钟下降沿
//        HAL_GPIO_WritePin(PIN_CLK_GPIO_Port, PIN_CLK_Pin, GPIO_PIN_RESET);
//        delay_us(5); // 延时10微秒
//        
//        // 读取数据位
//        ret |= HAL_GPIO_ReadPin(PIN_FOWARD_DAT_GPIO_Port, PIN_FOWARD_DAT_Pin) << i;
//        
//        // 时钟上升沿，让传感器准备下一位数据
//        HAL_GPIO_WritePin(PIN_CLK_GPIO_Port, PIN_CLK_Pin, GPIO_PIN_SET);
//        delay_us(5); // 延时10微秒
//    }
    
//    // 根据传感器特性处理数据
//    ret_8 = (ret >> 1) & 0xFF; 
//    return ret_8;
		return ret;
}




/**** 从一个变量分离出所有的bit ****/
void extractSensorData(uint8_t sensor_data, uint8_t sensor[8]) 
{
    for (int i = 0; i < 8; i++) {
        sensor[i] = (sensor_data >> i) & 1;
    }
}





/**** 从sensor里得到error和a的值 ****/
void GetErrorandA(void)
{
    /********************************************* 巡线 ********************************************/
    if(sensor[0]==1 && sensor[1]==1 && sensor[2]==1 && sensor[3]==1 && sensor[4]==1 && sensor[5]==1 && sensor[6]==1 && sensor[7]==1)
    {// 如果丢线
        stop_flag = 1;
    } 


    if ((sensor[0]==1 && sensor[1]==1 && sensor[2]==1 && sensor[3]==0 && sensor[4]==1 && sensor[5]==1 && sensor[6]==1 && sensor[7]==1) 
     || (sensor[0]==1 && sensor[1]==1 && sensor[2]==1 && sensor[3]==1 && sensor[4]==0 && sensor[5]==1 && sensor[6]==1 && sensor[7]==1)) 
    {// 线在中心，小车没偏
        error = 0; a = 0;
    }


    
    /********************************************* 偏右边 ******************************************/
    else if (sensor[0]==1 && sensor[1]==1 && sensor[2]==0 && sensor[3]==0 && sensor[4]==1 && sensor[5]==1 && sensor[6]==1 && sensor[7]==1)
    {// 小车偏右程度小
        error = -4; a = 1;
    }
    else if (sensor[0]==1 && sensor[1]==1 && sensor[2]==0 && sensor[3]==1 && sensor[4]==1 && sensor[5]==1 && sensor[6]==1 && sensor[7]==1)
    {// 小车偏右程度较小
        error = -5; a = 2;
    }
    else if (sensor[0]==1 && sensor[1]==0 && sensor[2]==0 && sensor[3]==1 && sensor[4]==1 && sensor[5]==1 && sensor[6]==1 && sensor[7]==1)
    {// 小车偏右程度适中
        error = -7; a = 3;
    }
    else if (sensor[0]==1 && sensor[1]==0 && sensor[2]==1 && sensor[3]==1 && sensor[4]==1 && sensor[5]==1 && sensor[6]==1 && sensor[7]==1)
    {// 小车偏右程度较大
        error = -9; a = 4;
    }
    else if (sensor[0]==0 && sensor[1]==0 && sensor[2]==1 && sensor[3]==1 && sensor[4]==1 && sensor[5]==1 && sensor[6]==1 && sensor[7]==1)
    {// 小车偏右程度大
        error = -11; a = 5;
    }
    else if (sensor[0]==0 && sensor[1]==1 && sensor[2]==1 && sensor[3]==1 && sensor[4]==1 && sensor[5]==1 && sensor[6]==1 && sensor[7]==1)
    {// 小车偏右程度剧烈
        error = -13; a = 6;
    }

    /********************************************* 偏左边 ******************************************/ 
    else if (sensor[0]==1 && sensor[1]==1 && sensor[2]==1 && sensor[3]==1 && sensor[4]==0 && sensor[5]==0 && sensor[6]==1 && sensor[7]==1)
    {// 小车偏左程度小
        error = 4; a = 1;
    }
    else if (sensor[0]==1 && sensor[1]==1 && sensor[2]==1 && sensor[3]==1 && sensor[4]==1 && sensor[5]==0 && sensor[6]==1 && sensor[7]==1)
    {// 小车偏左程度较小
        error = 5; a = 2;
    }
    else if (sensor[0]==1 && sensor[1]==1 && sensor[2]==1 && sensor[3]==1 && sensor[4]==1 && sensor[5]==0 && sensor[6]==0 && sensor[7]==1)
    {// 小车偏左程度适中
        error = 7; a = 3;
    }
    else if (sensor[0]==1 && sensor[1]==1 && sensor[2]==1 && sensor[3]==1 && sensor[4]==1 && sensor[5]==1 && sensor[6]==0 && sensor[7]==1)
    {// 小车偏左程度较大
        error = 9; a = 4;
    }
    else if (sensor[0]==1 && sensor[1]==1 && sensor[2]==1 && sensor[3]==1 && sensor[4]==1 && sensor[5]==1 && sensor[6]==0 && sensor[7]==0)
    {// 小车偏左程度大
        error = 11; a = 5;
    }
    else if (sensor[0]==1 && sensor[1]==1 && sensor[2]==1 && sensor[3]==1 && sensor[4]==1 && sensor[5]==1 && sensor[6]==1 && sensor[7]==0)
    {// 小车偏左程度剧烈
        error = 13; a = 6;
    }

}






/**** 获取灰度传感器中零的数量 ****/
uint8_t NumofZero(void)
{
    uint8_t i = 0, count = 0;
    for(; i<8; ++i)
    {// 遍历sensor数组，查询零的数量，可判断是否到弯道和停车
        if (sensor[i] == 0) {
            count++;
        }
    }
    return count;
}
