#include "mydefine.h"

//小状态机
#define  Q2_STATE_1   1
#define  Q2_STATE_2   2
#define  Q2_STATE_3   3
#define  Q2_STATE_4   4
#define  Q2_STATE_5   5

#define  Q3_STATE_1   1
#define  Q3_STATE_2   2
#define  Q3_STATE_3   3
#define  Q3_STATE_4   4
#define  Q3_STATE_5   5
#define  Q3_STATE_6   6
#define  Q3_STATE_7   7

//状态机结构体
struct state_machine State_Machine;

//初始化标志位
int q1_first_flag = 0;
int q2_first_flag = 0;
int q3_first_flag = 0;
int q4_first_flag = 0;

//圈数
int q4_circle_num = 0;

//循迹累计数
int step = 0;

//状态机初始化函数
void State_Machine_init(void)
{
    State_Machine.Main_State = STOP_STATE;
    State_Machine.Q1_State = STOP_STATE;
    State_Machine.Q2_State = STOP_STATE;
    State_Machine.Q3_State = STOP_STATE;
    State_Machine.Q4_State = STOP_STATE;
}

int target_angle = 0;

//任务1
void Question_Task_1(void)
{
    //任务1初始化
    if(q1_first_flag == 0)
    {        
        angle_pid_init();	//角度pid初始化       
        target_angle = 0;	//记录目标角度       
//        remind_flag = 1;//响铃      
        q1_first_flag = 1;//初始化禁用
    }

    //退出条件：检测到黑线同时满足最少里程数
    while (!(Digtal != 255 && distance > 1500))
    {
        
        angle_control(target_angle);//跑直
    }

    
//    remind_flag = 1;										//响铃
    motor_break();												//刹车
    q1_first_flag = 0;										//任务1初始化标志位恢复
    distance = 0;													//里程数清零
    State_Machine.Main_State = STOP_STATE;//恢复暂停状态
}

// void Question_Task_1(void) {;}
void Question_Task_2(void) {;}
void Question_Task_3(void) {;}
void Question_Task_4(void) {;}
