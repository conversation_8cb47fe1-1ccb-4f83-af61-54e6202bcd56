#ifndef __APP_QUESTION_TASK_H
#define __APP_QUESTION_TASK_H

//任务设定
#define  STOP_STATE   0
#define  QUESTION_1   1
#define  QUESTION_2   2
#define  QUESTION_3   3
#define  QUESTION_4   4

//任务状态机结构体
struct state_machine
{
	int Main_State;
	int Q1_State;
	int Q2_State;
	int Q3_State;
	int Q4_State;
};

extern struct state_machine State_Machine;
extern int step;

void State_Machine_init(void);
void Question_Task_1(void);
void Question_Task_2(void);
void Question_Task_3(void);
void Question_Task_4(void);

#endif
