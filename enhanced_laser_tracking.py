from maix import image, display, app, time, camera, touchscreen
import cv2
import numpy as np
import math
import gc
from micu_uart_lib import (
    SimpleUART, micu_printf, bind_variable,
    VariableContainer, clear_variable_bindings
)

# --------------------------- 配置参数区（所有过滤条件集中在这里） ---------------------------
# 矩形检测核心参数
MIN_CONTOUR_AREA = 500       # 最小轮廓面积（过滤小目标）
MAX_CONTOUR_AREA = 60000     # 最大轮廓面积（过滤大目标）
TARGET_SIDES = 4             # 目标边数（矩形为4）
BINARY_THRESHOLD = 66        # 二值化阈值

# 宽高比过滤参数（宽/高）
MIN_ASPECT_RATIO = 0.6       # 最小宽高比（高不超过宽的1.67倍）
MAX_ASPECT_RATIO = 1.7       # 最大宽高比（宽不超过高的1.7倍）

# 角度过滤参数（°）
MIN_ANGLE = 75               # 最小直角角度（接近90°）
MAX_ANGLE = 105               # 最大直角角度（接近90°）

# 对边长度一致性参数（比例）
MIN_OPPOSITE_RATIO = 0.7     # 最小对边比例（允许±20%偏差）
MAX_OPPOSITE_RATIO = 1.3     # 最大对边比例

# 透视变换与圆形参数
CORRECTED_WIDTH = 200        # 校正后矩形宽度
CORRECTED_HEIGHT = 150       # 校正后矩形高度
CIRCLE_RADIUS = 50           # 圆形轨迹半径
CIRCLE_NUM_POINTS = 12       # 圆形轨迹点数量

# 触摸按键参数
TOUCH_DEBOUNCE = 0.3         # 触摸防抖动时间（秒）

# 【新增】蓝紫光检测参数
PURPLE_HSV_LOWER = np.array([120, 50, 50])    # 蓝紫色HSV下限
PURPLE_HSV_UPPER = np.array([160, 255, 255])  # 蓝紫色HSV上限
MIN_LASER_AREA = 10          # 最小激光点面积
MAX_LASER_AREA = 500         # 最大激光点面积
LASER_BLUR_KERNEL = 5        # 高斯模糊核大小

# --------------------------- 增强版紫色激光检测器类 ---------------------------
class PurpleLaserDetector:
    def __init__(self, pixel_radius=3):
        self.pixel_radius = pixel_radius
        self.kernel = np.ones((3, 3), np.uint8)
        self.hsv_lower = PURPLE_HSV_LOWER
        self.hsv_upper = PURPLE_HSV_UPPER
        
    def detect(self, img):
        """
        检测蓝紫色激光点
        返回: (处理后的图像, 激光点坐标列表)
        """
        laser_points = []
        
        try:
            # 1. 颜色空间转换
            hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
            
            # 2. 高斯模糊减少噪声
            blurred = cv2.GaussianBlur(hsv, (LASER_BLUR_KERNEL, LASER_BLUR_KERNEL), 0)
            
            # 3. 颜色范围过滤
            mask = cv2.inRange(blurred, self.hsv_lower, self.hsv_upper)
            
            # 4. 形态学操作去噪
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, self.kernel)
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, self.kernel)
            
            # 5. 查找轮廓
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 6. 处理每个轮廓
            for contour in contours:
                area = cv2.contourArea(contour)
                
                # 面积过滤
                if MIN_LASER_AREA <= area <= MAX_LASER_AREA:
                    # 计算质心
                    M = cv2.moments(contour)
                    if M["m00"] != 0:
                        cx = int(M["m10"] / M["m00"])
                        cy = int(M["m01"] / M["m00"])
                        laser_points.append((cx, cy, area))
                        
                        # 在原图上标记激光点
                        cv2.circle(img, (cx, cy), 8, (255, 0, 255), 2)  # 紫色圆圈
                        cv2.circle(img, (cx, cy), 3, (0, 255, 255), -1)  # 黄色实心点
                        
                        # 显示坐标
                        coord_text = f"({cx},{cy})"
                        cv2.putText(img, coord_text, (cx + 10, cy - 10),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 0), 1)
            
            # 7. 显示检测掩码（调试用，可选）
            # 在图像右上角显示小窗口
            mask_small = cv2.resize(mask, (80, 60))
            mask_bgr = cv2.cvtColor(mask_small, cv2.COLOR_GRAY2BGR)
            img[10:70, 230:310] = mask_bgr
            cv2.rectangle(img, (230, 10), (310, 70), (255, 255, 255), 1)
            cv2.putText(img, "Laser", (235, 85), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)
            
        except Exception as e:
            print(f"激光检测错误: {e}")
        
        return img, laser_points
    
    def adjust_hsv_range(self, h_offset=0, s_offset=0, v_offset=0):
        """动态调整HSV检测范围"""
        self.hsv_lower[0] = max(0, min(179, self.hsv_lower[0] + h_offset))
        self.hsv_upper[0] = max(0, min(179, self.hsv_upper[0] + h_offset))
        self.hsv_lower[1] = max(0, min(255, self.hsv_lower[1] + s_offset))
        self.hsv_upper[1] = max(0, min(255, self.hsv_upper[1] + s_offset))
        self.hsv_lower[2] = max(0, min(255, self.hsv_lower[2] + v_offset))
        self.hsv_upper[2] = max(0, min(255, self.hsv_upper[2] + v_offset))

# --------------------------- 增强版虚拟按键类 ---------------------------
class VirtualButtons:
    def __init__(self):
        self.buttons = [
            [20, 180, 45, 20, "Center", "center"],    
            [115, 180, 50, 20, "Circle", "circle"],   
            [180, 180, 25, 20, "T-", "thresh_down"],  
            [210, 180, 25, 20, "T+", "thresh_up"],
            # 【新增】激光相关按键
            [250, 180, 35, 20, "L-", "laser_down"],   # 激光HSV调节
            [290, 180, 35, 20, "L+", "laser_up"],     # 激光HSV调节
        ]

        self.touch_areas = [
            [40, 365, 85, 35],     
            [220, 350, 110, 70],     
            [330, 265, 50, 40],     
            [390, 265, 50, 40],
            # 【新增】激光按键触摸区域
            [440, 265, 50, 40],     # L- 按键
            [500, 265, 50, 40],     # L+ 按键
        ]

        self.last_touch_time = 0
        self.touch_debounce = TOUCH_DEBOUNCE

    def check_touch(self, touch_x, touch_y):
        current_time = time.time()
        if current_time - self.last_touch_time < self.touch_debounce:
            return None

        for i, touch_area in enumerate(self.touch_areas):
            area_x, area_y, area_w, area_h = touch_area
            if area_x <= touch_x <= area_x + area_w and area_y <= touch_y <= area_y + area_h:
                self.last_touch_time = current_time
                if i < len(self.buttons):
                    return self.buttons[i][5]
        return None

    def draw_buttons(self, img, current_mode, threshold=BINARY_THRESHOLD, laser_count=0):
        for button in self.buttons:
            x, y, w, h, text, action = button
            if (action == "center" and current_mode == "center") or (action == "circle" and current_mode == "circle"):
                color = (0, 255, 255)
                thickness = 3
            elif action in ["thresh_up", "thresh_down"]:
                color = (0, 255, 0)
                thickness = 2
            elif action in ["laser_up", "laser_down"]:
                color = (255, 0, 255)  # 紫色表示激光相关
                thickness = 2
            else:
                color = (255, 255, 255)
                thickness = 2
            cv2.rectangle(img, (x, y), (x + w, y + h), color, thickness)
            text_x = x + (w - len(text) * 4) // 2
            text_y = y + (h + 6) // 2
            cv2.putText(img, text, (text_x, text_y), cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
        
        # 显示参数信息
        cv2.putText(img, f"Thresh: {threshold}", (180, 170),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
        cv2.putText(img, f"Laser: {laser_count}", (250, 170),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 255), 1)

# 触摸屏初始化
def init_touchscreen():
    try:
        ts = touchscreen.TouchScreen()
        print("TouchScreen initialized successfully")
        return ts
    except Exception as e:
        print(f"TouchScreen init failed: {e}")
        return None

# --------------------------- 工具函数 ---------------------------
def generate_circle_points(center, radius, num_points):
    circle_points = []
    cx, cy = center
    for i in range(num_points):
        angle = 2 * math.pi * i / num_points
        x = int(cx + radius * math.cos(angle))
        y = int(cy + radius * math.sin(angle))
        circle_points.append((x, y))
    return circle_points

def perspective_transform(pts, target_width, target_height):
    s = pts.sum(axis=1)
    tl = pts[np.argmin(s)]
    br = pts[np.argmax(s)]
    diff = np.diff(pts, axis=1)
    tr = pts[np.argmin(diff)]
    bl = pts[np.argmax(diff)]
    src_pts = np.array([tl, tr, br, bl], dtype=np.float32)
    
    dst_pts = np.array([
        [0, 0], [target_width-1, 0],
        [target_width-1, target_height-1], [0, target_height-1]
    ], dtype=np.float32)
    
    M = cv2.getPerspectiveTransform(src_pts, dst_pts)
    ret, M_inv = cv2.invert(M)
    return M, M_inv, src_pts

def is_regular_rectangle(approx):
    """使用配置参数判断是否为规则矩形"""
    # 1. 凸性检查
    if not cv2.isContourConvex(approx):
        return False, "非凸多边形"
    
    # 2. 提取四个顶点
    pts = approx.reshape(4, 2).astype(np.float32)
    p0, p1, p2, p3 = pts[0], pts[1], pts[2], pts[3]
    
    # 3. 计算四条边的长度
    edge_lengths = [
        math.hypot(p1[0]-p0[0], p1[1]-p0[1]),  # 上边
        math.hypot(p2[0]-p1[0], p2[1]-p1[1]),  # 右边
        math.hypot(p3[0]-p2[0], p3[1]-p2[1]),  # 下边
        math.hypot(p0[0]-p3[0], p0[1]-p3[1])   # 左边
    ]
    top, right, bottom, left = edge_lengths
    
    # 4. 校验对边长度（使用配置参数）
    if not (MIN_OPPOSITE_RATIO <= top/bottom <= MAX_OPPOSITE_RATIO and 
            MIN_OPPOSITE_RATIO <= left/right <= MAX_OPPOSITE_RATIO):
        return False, f"对边不等（上/下={top/bottom:.2f}, 左/右={left/right:.2f}"
    
    # 5. 计算四个角的角度（使用配置参数）
    angles = []
    for i in range(4):
        p_prev = pts[i]
        p_curr = pts[(i+1)%4]
        p_next = pts[(i+2)%4]
        # 计算向量
        v1 = [p_curr[0]-p_prev[0], p_curr[1]-p_prev[1]]
        v2 = [p_next[0]-p_curr[0], p_next[1]-p_curr[1]]
        # 计算夹角（度）
        dot = v1[0]*v2[0] + v1[1]*v2[1]
        det = v1[0]*v2[1] - v1[1]*v2[0]
        angle = abs(math.degrees(math.atan2(det, dot)))
        angles.append(angle)
    
    if not all(MIN_ANGLE <= angle <= MAX_ANGLE for angle in angles):
        return False, f"角度异常 {[round(a,1) for a in angles]}"
    
    # 所有条件通过
    return True, "规则矩形"

# 【新增】目标点更新函数
def update_target_from_laser(laser_points, current_target):
    """根据激光点坐标更新目标点"""
    if laser_points:
        # 选择面积最大的激光点作为新目标
        largest_laser = max(laser_points, key=lambda x: x[2])
        return (largest_laser[0], largest_laser[1])
    return current_target

# --------------------------- 主程序 ---------------------------
if __name__ == "__main__":
    gc.disable()
    print("增强版激光跟踪程序启动...")
    print("新增功能：蓝紫光检测与坐标显示")
    
    # 初始化设备
    disp = display.Display()
    cam = camera.Camera(320, 240, image.Format.FMT_BGR888)
    laser_detector = PurpleLaserDetector()
    
    # 初始化虚拟按键和触摸屏
    buttons = VirtualButtons()
    ts = init_touchscreen()
    current_mode = "center"
    last_touch_pos = (0, 0)
    binary_threshold = BINARY_THRESHOLD
    
    # 【新增】目标点管理
    target_x, target_y = 210, 155  # 默认目标点
    auto_target_update = False     # 是否自动更新目标点
    
    # 初始化串口
    uart = SimpleUART()
    if uart.init("/dev/ttyS0", 115200, set_as_global=True):
        print("串口初始化成功")
        uart.set_frame("", "", False)
    else:
        print("串口初始化失败")
        exit()

    # FPS计算初始化
    fps = 0
    last_time = time.ticks_ms()
    frame_count = 0

    while not app.need_exit():
        frame_count += 1
        
        # 处理触摸输入
        current_time = time.time()
        if ts and (current_time - buttons.last_touch_time) > buttons.touch_debounce:
            try:
                if ts.available():
                    touch_data = ts.read()
                    if len(touch_data) >= 3:
                        touch_x, touch_y, pressed = touch_data[0], touch_data[1], touch_data[2]
                        last_touch_pos = (touch_x, touch_y)
                        if pressed:
                            action = buttons.check_touch(touch_x, touch_y)
                            if action:
                                buttons.last_touch_time = current_time
                                if action == "center":
                                    current_mode = "center"
                                    print("切换到中心点模式")
                                elif action == "circle":
                                    current_mode = "circle"
                                    print("切换到圆形模式")
                                elif action == "thresh_up":
                                    binary_threshold = min(255, binary_threshold + 3)
                                    print(f"阈值增加到: {binary_threshold}")
                                elif action == "thresh_down":
                                    binary_threshold = max(1, binary_threshold - 3)
                                    print(f"阈值减少到: {binary_threshold}")
                                # 【新增】激光HSV调节
                                elif action == "laser_up":
                                    laser_detector.adjust_hsv_range(h_offset=2)
                                    print("激光检测范围增加")
                                elif action == "laser_down":
                                    laser_detector.adjust_hsv_range(h_offset=-2)
                                    print("激光检测范围减少")
            except Exception as e:
                if frame_count % 120 == 0:
                    print(f"Touch processing error: {e}")
        
        # 计算FPS
        current_time_ms = time.ticks_ms()
        if current_time_ms - last_time > 0:
            fps = 1000.0 / (current_time_ms - last_time)
        last_time = current_time_ms
        
        # 读取图像
        img = cam.read()
        if img is None:
            continue
        img_cv = image.image2cv(img, ensure_bgr=False, copy=False)
        output = img_cv.copy()

        # 1. 【增强】激光检测 - 现在真正工作了！
        output, laser_points = laser_detector.detect(output)
        
        # 【新增】根据激光点更新目标点
        if laser_points and auto_target_update:
            target_x, target_y = update_target_from_laser(laser_points, (target_x, target_y))

        # 2. 矩形检测（使用配置参数过滤）
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        _, binary = cv2.threshold(gray, binary_threshold, 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        
        quads = []
        for cnt in contours:
            area = cv2.contourArea(cnt)
            # 1. 面积过滤（使用配置参数）
            if not (MIN_CONTOUR_AREA < area < MAX_CONTOUR_AREA):
                continue
            
            # 2. 多边形逼近与边数过滤（使用配置参数）
            epsilon = 0.03 * cv2.arcLength(cnt, True)
            approx = cv2.approxPolyDP(cnt, epsilon, True)
            if len(approx) != TARGET_SIDES:
                continue
            
            # 3. 宽高比过滤（使用配置参数）
            x, y, w, h = cv2.boundingRect(approx)
            if h == 0:
                continue
            aspect_ratio = w / h
            if not (MIN_ASPECT_RATIO <= aspect_ratio <= MAX_ASPECT_RATIO):
                continue
            
            # 4. 规则性校验（使用配置参数）
            is_regular, reason = is_regular_rectangle(approx)
            if not is_regular:
                continue
            
            # 所有条件通过
            quads.append((approx, area))

        # 只保留面积最大的矩形
        inner_quads = []
        if quads:
            largest_quad = max(quads, key=lambda x: x[1])
            inner_quads = [largest_quad]

        # 3. 处理检测到的矩形
        center_points = []
        all_circle_points = []

        for approx, area in inner_quads:
            pts = approx.reshape(4, 2).astype(np.float32)
            cv2.drawContours(output, [approx], -1, (0, 255, 0), 2)

            # 透视变换获取中心点（使用配置参数）
            M, M_inv, src_pts = perspective_transform(pts, CORRECTED_WIDTH, CORRECTED_HEIGHT)
            if M_inv is not None:
                corrected_center = (CORRECTED_WIDTH//2, CORRECTED_HEIGHT//2)
                center_np = np.array([[corrected_center]], dtype=np.float32)
                original_center = cv2.perspectiveTransform(center_np, M_inv)[0][0]
                cx, cy = int(original_center[0]), int(original_center[1])
                cv2.circle(output, (cx, cy), 5, (255, 0, 0), -1)
                center_points.append((cx, cy))
            else:
                cx = int(np.mean(pts[:, 0]))
                cy = int(np.mean(pts[:, 1]))
                cv2.circle(output, (cx, cy), 5, (255, 0, 0), -1)
                center_points.append((cx, cy))

            # 圆形模式处理（使用配置参数）
            if current_mode == "circle":
                if M_inv is not None:
                    corrected_center = (CORRECTED_WIDTH//2, CORRECTED_HEIGHT//2)
                    corrected_circle = generate_circle_points(
                        corrected_center, CIRCLE_RADIUS, CIRCLE_NUM_POINTS
                    )
                    corrected_points_np = np.array([corrected_circle], dtype=np.float32)
                    original_points = cv2.perspectiveTransform(corrected_points_np, M_inv)[0]
                    original_points = [(int(x), int(y)) for x, y in original_points]
                    all_circle_points.extend(original_points)
                    for (x, y) in original_points:
                        cv2.circle(output, (x, y), 2, (0, 0, 255), -1)
                else:
                    simple_circle = generate_circle_points((cx, cy), 30, CIRCLE_NUM_POINTS)
                    all_circle_points.extend(simple_circle)
                    for (x, y) in simple_circle:
                        cv2.circle(output, (x, y), 2, (0, 0, 255), -1)

        # 4. 串口发送数据
        if current_mode == "center":
            if center_points:
                cx, cy = center_points[0]
                micu_printf(f"R,{cx},{cy}")
                time.sleep(0.1)
            else:
                micu_printf("R,0,0")
                time.sleep(0.1)
        elif current_mode == "circle":
            if all_circle_points:
                circle_data = f"C,{len(all_circle_points)}"
                for (x, y) in all_circle_points:
                    circle_data += f",{x},{y}"
                micu_printf(circle_data)
                time.sleep(0.1)

        # 5. 【增强】绘制目标点标记 - 现在可以动态更新
        cross_size = 5
        cv2.line(output, (target_x - cross_size, target_y), (target_x + cross_size, target_y), (255, 0, 255), 2)
        cv2.line(output, (target_x, target_y - cross_size), (target_x, target_y + cross_size), (255, 0, 255), 2)
        cv2.putText(output, f"Target({target_x},{target_y})", (target_x + 8, target_y - 8),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)

        # 6. 【增强】绘制虚拟按键 - 显示激光点数量
        buttons.draw_buttons(output, current_mode, binary_threshold, len(laser_points))

        # 7. 【增强】显示统计信息 - 包含激光信息
        cv2.putText(output, f"FPS: {fps:.1f}", (10, 20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        mode_text = f"Mode: {current_mode.upper()}"
        cv2.putText(output, mode_text, (10, 40),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)
        cv2.putText(output, f"Touch: {last_touch_pos}", (10, 55),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 0), 1)
        
        # 【新增】激光点信息显示
        if laser_points:
            laser_info = f"Laser Points: {len(laser_points)}"
            cv2.putText(output, laser_info, (10, 70),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)
            # 显示最大激光点坐标
            largest_laser = max(laser_points, key=lambda x: x[2])
            laser_coord = f"Main Laser: ({largest_laser[0]},{largest_laser[1]})"
            cv2.putText(output, laser_coord, (10, 85),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)

        # 显示图像
        img_show = image.cv2image(output, bgr=True, copy=False)
        disp.show(img_show)