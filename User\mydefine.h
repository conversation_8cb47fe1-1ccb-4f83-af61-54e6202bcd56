#ifndef __MYDEFINE_H_
#define __MYDEFINE_H_

/*��׼C�⺯��*/
#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include <math.h>
#include <stdint.h>
#include <stdbool.h>

/*HAL��*/
#include "main.h"


#include "Emm_V5.h"
#include "step_motor_bsp.h"
#include "pi_bsp.h"
#include "uart_bsp.h"






#include "gray_sensor.h"


/*�м��*/
#include "mid_pid.h"//λ��ʽpid����
#include "mid_incremental_pid.h"//����ʽpid����

//Ӧ�ò�

//#include "app_position_pid.h"//ѭ��pid
#include "app_angle_control.h"//�Ƕ�pid
#include "app_speed_pid.h"//����pid
#include "app_distance_pid.h"//����pid
#include "app_gyro_pid.h"//����תpid
#include "app_question_task.h"//����״̬��




/*APP*/
#include "usart_app.h"
#include "led_app.h"
#include "key_app.h"
#include "oled_app.h"
#include "motor_app.h"
#include "encoder_app.h"
#include "gray_app.h"
#include "my_timer.h"
#include "bno08x_app.h"

/*������*/
#include "my_scheduler.h"


extern uint8_t task_num;



extern uint8_t sensor[8]; // main.c�ж���ĻҶȴ�����ֵ
extern uint8_t stop_flag; // ͣ����־λ










extern struct rt_ringbuffer ringbuffer_x;
extern struct rt_ringbuffer ringbuffer_y;
extern struct rt_ringbuffer ringbuffer_pi;

extern uint8_t motor_x_buf[64];
extern uint8_t motor_y_buf[64];
extern uint8_t pi_rx_buf[64];

extern uint8_t ringbuffer_pool_x[64];
extern uint8_t ringbuffer_pool_y[64];
extern uint8_t ringbuffer_pool_pi[256];

extern uint8_t output_buffer_x[64];
extern uint8_t output_buffer_y[64];
extern uint8_t output_buffer_pi[256];


// USART1
extern uint8_t uart_rx_dma_buffer[128];
extern uint8_t uart_dma_buffer[128];
extern struct rt_ringbuffer uart_ringbuffer;
extern uint8_t ringbuffer_pool[128];
extern uint8_t cam_con_flag;


#endif
