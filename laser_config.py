"""
蓝紫光检测参数配置文件
可以根据实际环境调整这些参数来优化检测效果
"""

import numpy as np

# =============================================================================
# 蓝紫光HSV颜色范围配置
# =============================================================================

# 标准蓝紫色范围 (推荐用于大多数情况)
PURPLE_HSV_LOWER_STANDARD = np.array([120, 50, 50])   # 色调120, 饱和度50, 亮度50
PURPLE_HSV_UPPER_STANDARD = np.array([160, 255, 255]) # 色调160, 饱和度255, 亮度255

# 深蓝紫色范围 (用于较暗的激光)
PURPLE_HSV_LOWER_DARK = np.array([110, 80, 30])
PURPLE_HSV_UPPER_DARK = np.array([150, 255, 200])

# 亮蓝紫色范围 (用于较亮的激光)
PURPLE_HSV_LOWER_BRIGHT = np.array([130, 30, 100])
PURPLE_HSV_UPPER_BRIGHT = np.array([170, 255, 255])

# 宽范围检测 (用于不确定的环境)
PURPLE_HSV_LOWER_WIDE = np.array([100, 30, 30])
PURPLE_HSV_UPPER_WIDE = np.array([180, 255, 255])

# =============================================================================
# 激光点大小过滤参数
# =============================================================================

# 标准设置
MIN_LASER_AREA_STANDARD = 10      # 最小激光点面积
MAX_LASER_AREA_STANDARD = 500     # 最大激光点面积

# 精细检测 (检测小激光点)
MIN_LASER_AREA_FINE = 5
MAX_LASER_AREA_FINE = 200

# 粗糙检测 (只检测大激光点)
MIN_LASER_AREA_COARSE = 50
MAX_LASER_AREA_COARSE = 1000

# =============================================================================
# 图像处理参数
# =============================================================================

# 高斯模糊核大小 (奇数)
BLUR_KERNEL_SMALL = 3      # 轻微模糊
BLUR_KERNEL_STANDARD = 5   # 标准模糊
BLUR_KERNEL_LARGE = 7      # 强模糊

# 形态学操作核大小
MORPH_KERNEL_SMALL = np.ones((2, 2), np.uint8)
MORPH_KERNEL_STANDARD = np.ones((3, 3), np.uint8)
MORPH_KERNEL_LARGE = np.ones((5, 5), np.uint8)

# =============================================================================
# 预设配置组合
# =============================================================================

# 室内环境配置
INDOOR_CONFIG = {
    'hsv_lower': PURPLE_HSV_LOWER_STANDARD,
    'hsv_upper': PURPLE_HSV_UPPER_STANDARD,
    'min_area': MIN_LASER_AREA_STANDARD,
    'max_area': MAX_LASER_AREA_STANDARD,
    'blur_kernel': BLUR_KERNEL_STANDARD,
    'morph_kernel': MORPH_KERNEL_STANDARD,
    'description': '室内标准光照环境'
}

# 室外环境配置
OUTDOOR_CONFIG = {
    'hsv_lower': PURPLE_HSV_LOWER_BRIGHT,
    'hsv_upper': PURPLE_HSV_UPPER_BRIGHT,
    'min_area': MIN_LASER_AREA_COARSE,
    'max_area': MAX_LASER_AREA_COARSE,
    'blur_kernel': BLUR_KERNEL_LARGE,
    'morph_kernel': MORPH_KERNEL_LARGE,
    'description': '室外强光环境'
}

# 暗光环境配置
LOW_LIGHT_CONFIG = {
    'hsv_lower': PURPLE_HSV_LOWER_DARK,
    'hsv_upper': PURPLE_HSV_UPPER_DARK,
    'min_area': MIN_LASER_AREA_FINE,
    'max_area': MAX_LASER_AREA_STANDARD,
    'blur_kernel': BLUR_KERNEL_SMALL,
    'morph_kernel': MORPH_KERNEL_SMALL,
    'description': '暗光环境'
}

# 高精度配置
HIGH_PRECISION_CONFIG = {
    'hsv_lower': PURPLE_HSV_LOWER_STANDARD,
    'hsv_upper': PURPLE_HSV_UPPER_STANDARD,
    'min_area': MIN_LASER_AREA_FINE,
    'max_area': MAX_LASER_AREA_FINE,
    'blur_kernel': BLUR_KERNEL_SMALL,
    'morph_kernel': MORPH_KERNEL_SMALL,
    'description': '高精度小目标检测'
}

# 宽容配置 (检测范围最大)
TOLERANT_CONFIG = {
    'hsv_lower': PURPLE_HSV_LOWER_WIDE,
    'hsv_upper': PURPLE_HSV_UPPER_WIDE,
    'min_area': MIN_LASER_AREA_FINE,
    'max_area': MAX_LASER_AREA_COARSE,
    'blur_kernel': BLUR_KERNEL_STANDARD,
    'morph_kernel': MORPH_KERNEL_STANDARD,
    'description': '宽容检测模式'
}

# =============================================================================
# 配置选择函数
# =============================================================================

def get_config(config_name='standard'):
    """
    获取指定的配置
    
    参数:
        config_name: 配置名称
            - 'indoor': 室内环境
            - 'outdoor': 室外环境  
            - 'low_light': 暗光环境
            - 'high_precision': 高精度模式
            - 'tolerant': 宽容模式
            - 'standard': 标准配置 (默认)
    
    返回:
        配置字典
    """
    configs = {
        'indoor': INDOOR_CONFIG,
        'outdoor': OUTDOOR_CONFIG,
        'low_light': LOW_LIGHT_CONFIG,
        'high_precision': HIGH_PRECISION_CONFIG,
        'tolerant': TOLERANT_CONFIG,
        'standard': INDOOR_CONFIG  # 默认使用室内配置
    }
    
    config = configs.get(config_name, INDOOR_CONFIG)
    print(f"使用配置: {config['description']}")
    return config

def print_all_configs():
    """打印所有可用配置"""
    configs = [
        ('indoor', INDOOR_CONFIG),
        ('outdoor', OUTDOOR_CONFIG),
        ('low_light', LOW_LIGHT_CONFIG),
        ('high_precision', HIGH_PRECISION_CONFIG),
        ('tolerant', TOLERANT_CONFIG)
    ]
    
    print("可用配置:")
    print("-" * 50)
    for name, config in configs:
        print(f"{name:15} - {config['description']}")
        print(f"{'':15}   HSV: {config['hsv_lower']} - {config['hsv_upper']}")
        print(f"{'':15}   面积: {config['min_area']} - {config['max_area']}")
        print()

# =============================================================================
# HSV颜色调试工具
# =============================================================================

def create_hsv_test_image(h_range=(120, 160), s_range=(50, 255), v_range=(50, 255)):
    """
    创建HSV测试图像，用于调试颜色范围
    
    参数:
        h_range: 色调范围 (0-179)
        s_range: 饱和度范围 (0-255)  
        v_range: 亮度范围 (0-255)
    
    返回:
        BGR格式的测试图像
    """
    import cv2
    
    # 创建HSV渐变图像
    h_start, h_end = h_range
    s_start, s_end = s_range
    v_start, v_end = v_range
    
    height, width = 100, 300
    hsv_img = np.zeros((height, width, 3), dtype=np.uint8)
    
    for x in range(width):
        # 色调渐变
        h = int(h_start + (h_end - h_start) * x / width)
        for y in range(height):
            # 饱和度和亮度渐变
            s = int(s_start + (s_end - s_start) * y / height)
            v = int(v_start + (v_end - v_start) * y / height)
            hsv_img[y, x] = [h, s, v]
    
    # 转换为BGR
    bgr_img = cv2.cvtColor(hsv_img, cv2.COLOR_HSV2BGR)
    return bgr_img

# =============================================================================
# 使用示例
# =============================================================================

if __name__ == "__main__":
    print("蓝紫光检测配置文件")
    print("=" * 50)
    
    # 显示所有配置
    print_all_configs()
    
    # 获取特定配置示例
    config = get_config('indoor')
    print(f"选择的配置: {config}")
    
    print("\n使用方法:")
    print("from laser_config import get_config")
    print("config = get_config('indoor')")
    print("detector.hsv_lower = config['hsv_lower']")
    print("detector.hsv_upper = config['hsv_upper']")